# 🚀 Mobile Carwash Server - CI/CD with GitHub Actions

This repository includes a complete CI/CD pipeline setup using GitHub Actions for automated deployment to DigitalOcean droplets.

## 📋 Quick Start

### 1. Setup GitHub Secrets

Run the helper script to generate SSH keys and get the secrets template:

```bash
chmod +x scripts/setup-github-secrets.sh
./scripts/setup-github-secrets.sh setup
```

Add the generated secrets to your GitHub repository:
- Go to **Settings** → **Secrets and variables** → **Actions**
- Add all the secrets shown by the script

### 2. Setup DigitalOcean Servers

**Create two droplets:**
- **Development**: 1 GB RAM, Ubuntu 22.04 LTS
- **Staging**: 2 GB RAM, Ubuntu 22.04 LTS

**Install Docker on both servers:**
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
sudo mkdir -p /opt/mobile-carwash
```

**Add SSH keys to servers:**
```bash
# Copy the public keys generated by the setup script
echo "your-dev-public-key" >> ~/.ssh/authorized_keys
echo "your-staging-public-key" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

### 3. Test Your Setup

**Test SSH connectivity:**
```bash
./scripts/setup-github-secrets.sh test
```

**Push to trigger deployment:**
```bash
# Development deployment
git checkout develop
git push origin develop

# Staging deployment  
git checkout main
git push origin main
```

## 🔄 Workflows

### Development Deployment (`.github/workflows/deploy-dev.yml`)
- **Trigger**: Push to `develop` branch
- **Target**: Development server
- **Process**: Test → Build → Deploy → Health Check

### Staging Deployment (`.github/workflows/deploy-staging.yml`)
- **Trigger**: Push to `main` branch
- **Target**: Staging server  
- **Process**: Test → Security Scan → Build → Backup → Deploy → Verify → Rollback on failure

### Health Check (`.github/workflows/health-check.yml`)
- **Trigger**: Manual or scheduled (every 6 hours)
- **Process**: System checks → API tests → Performance tests

## 🔐 Required GitHub Secrets

### Development Environment
```
DEV_SERVER_IP=your.dev.server.ip
DEV_SERVER_USER=root
DEV_SSH_PRIVATE_KEY=[SSH private key content]
DEV_DB_URI=postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres
DEV_JWT_SECRET=[JWT secret]
DEV_STRIPE_SECRET_KEY=sk_test_[your_key]
```

### Staging Environment
```
STAGING_SERVER_IP=your.staging.server.ip
STAGING_SERVER_USER=root
STAGING_SSH_PRIVATE_KEY=[SSH private key content]
STAGING_DB_URI=postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres
STAGING_JWT_SECRET=[JWT secret]
STAGING_STRIPE_SECRET_KEY=sk_test_[your_key]
```

## 🎯 Features

✅ **Automated Testing** - TypeScript compilation and security audits  
✅ **Docker Builds** - Multi-stage builds with vulnerability scanning  
✅ **Zero-Downtime Deployment** - Rolling deployments with health checks  
✅ **Automatic Rollback** - Rollback on deployment failure  
✅ **Security Scanning** - Trivy vulnerability scanning  
✅ **Health Monitoring** - Automated health checks every 6 hours  
✅ **Backup Management** - Automatic backups before deployment  
✅ **Environment Isolation** - Separate dev and staging environments  

## 📊 Monitoring

### View Deployment Status
- Go to **GitHub** → **Actions** to see all workflow runs
- Click on any run to see detailed logs

### Manual Health Checks
1. Go to **Actions** → **Health Check**
2. Click **Run workflow**
3. Select environment and options
4. Click **Run workflow**

### Server Logs
```bash
# SSH to server
ssh <EMAIL>

# View application logs
cd /opt/mobile-carwash
sudo docker-compose logs -f app
```

## 🔧 Troubleshooting

### Common Issues

**SSH Connection Failed:**
- Verify server IP and SSH key in GitHub Secrets
- Check if public key is added to server's `~/.ssh/authorized_keys`
- Test SSH connection manually

**Docker Build Failed:**
- Check TypeScript compilation errors
- Verify all dependencies are available
- Review build logs in GitHub Actions

**Health Check Failed:**
- SSH to server and check container status
- Review application logs
- Verify environment variables

### Manual Rollback
```bash
# SSH to server
ssh <EMAIL>
cd /opt/mobile-carwash

# Check available backups
ls -la backups/

# Load previous version
sudo docker load < backups/mobile-carwash-[version].tar.gz
sudo docker tag mobile-carwash:[version] mobile-carwash:latest
sudo docker-compose up -d
```

## 📚 Documentation

- **[Complete Setup Guide](GITHUB_ACTIONS_SETUP.md)** - Detailed setup instructions
- **[Docker Guide](DOCKER.md)** - Docker configuration and usage
- **[Supabase Setup](SUPABASE_SETUP.md)** - Database configuration

## 🚀 Deployment Flow

```mermaid
graph TD
    A[Push to develop] --> B[Development Workflow]
    B --> C[Test & Build]
    C --> D[Deploy to Dev Server]
    D --> E[Health Check]
    
    F[Push to main] --> G[Staging Workflow]
    G --> H[Test & Security Scan]
    H --> I[Build & Scan Image]
    I --> J[Create Backup]
    J --> K[Deploy to Staging]
    K --> L[Health Check]
    L --> M{Success?}
    M -->|Yes| N[Complete]
    M -->|No| O[Auto Rollback]
```

## 🎉 Getting Started

1. **Run the setup script**: `./scripts/setup-github-secrets.sh`
2. **Add secrets to GitHub**: Copy the generated values
3. **Setup your servers**: Install Docker and add SSH keys
4. **Push code**: Trigger your first deployment!

For detailed instructions, see [GITHUB_ACTIONS_SETUP.md](GITHUB_ACTIONS_SETUP.md).

---

**Happy Deploying! 🚀**
