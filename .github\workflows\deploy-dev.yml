name: Deploy to Development

on:
  push:
    branches: [ develop ]
  pull_request:
    branches: [ develop ]

env:
  NODE_VERSION: '18'
  DOCKER_IMAGE: mobile-carwash
  DEPLOYMENT_PATH: /root/mobile_carwash_server

jobs:
  test:
    name: Test Application
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript type check
      run: npx tsc --noEmit
      
    - name: Run tests
      run: npm test || echo "No tests configured yet"
      
    - name: Run security audit
      run: npm audit --audit-level high || true

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build Docker image
      run: |
        docker build \
          --target production \
          --tag ${{ env.DOCKER_IMAGE }}:dev-${{ github.sha }} \
          --tag ${{ env.DOCKER_IMAGE }}:dev-latest \
          .
          
    - name: Test Docker image
      run: |
        # Test that the image can start
        docker run --rm -d \
          --name test-container \
          -e NODE_ENV=production \
          -e PORT=8080 \
          -e DB_URI="postgresql://test:test@localhost:5432/test" \
          -e JWT_SECRET="test-secret" \
          -e STRIPE_SECRET_KEY="sk_test_test" \
          -p 8080:8080 \
          ${{ env.DOCKER_IMAGE }}:dev-latest
          
        # Wait for container to start
        sleep 10
        
        # Check if container is running
        docker ps | grep test-container
        
        # Stop test container
        docker stop test-container
        
    - name: Save Docker image
      run: |
        docker save ${{ env.DOCKER_IMAGE }}:dev-${{ github.sha }} | gzip > mobile-carwash-dev.tar.gz
        
    - name: Upload Docker image artifact
      uses: actions/upload-artifact@v4
      with:
        name: docker-image-dev
        path: mobile-carwash-dev.tar.gz
        retention-days: 1

  deploy:
    name: Deploy to Development Server
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download Docker image artifact
      uses: actions/download-artifact@v4
      with:
        name: docker-image-dev
        
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.DEV_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.DEV_SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: Copy files to server
      run: |
        # Copy Docker image
        scp -i ~/.ssh/id_rsa mobile-carwash-dev.tar.gz ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }}:/tmp/
        
        # Copy docker-compose file
        scp -i ~/.ssh/id_rsa docker-compose.cloud.yml ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }}:/tmp/
        
    - name: Deploy to development server
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          set -e
          
          # Navigate to deployment directory
          cd ${{ env.DEPLOYMENT_PATH }}
          
          # Create environment file
          sudo tee .env > /dev/null << 'ENVEOF'
        NODE_ENV=development
        PORT=8080
        DB_URI=${{ secrets.DEV_DB_URI }}
        JWT_SECRET=${{ secrets.DEV_JWT_SECRET }}
        JWT_EXPIRES_IN=7d
        STRIPE_SECRET_KEY=${{ secrets.DEV_STRIPE_SECRET_KEY }}
        STRIPE_PUBLISHABLE_KEY=${{ secrets.DEV_STRIPE_PUBLISHABLE_KEY }}
        STRIPE_CURRENCY=${{ secrets.DEV_STRIPE_CURRENCY }}
        ENVEOF
          
          # Stop existing services
          sudo docker-compose -f docker-compose.cloud.yml down || true
          
          # Load new Docker image
          sudo docker load < /tmp/mobile-carwash-dev.tar.gz
          
          # Update docker-compose file
          sudo cp /tmp/docker-compose.cloud.yml .
          
          # Tag the new image
          sudo docker tag mobile-carwash:dev-${{ github.sha }} mobile-carwash:latest
          
          # Start services
          sudo docker-compose -f docker-compose.cloud.yml up -d
          
          # Clean up
          rm -f /tmp/mobile-carwash-dev.tar.gz /tmp/docker-compose.cloud.yml
          
          # Wait for services to be ready
          echo "Waiting for services to start..."
          sleep 30
          
          # Check if services are running
          sudo docker-compose -f docker-compose.cloud.yml ps
        EOF
        
    - name: Health check
      run: |
        # Wait a bit more for services to fully start
        sleep 30
        
        # Run health check
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          # Test health endpoint
          curl -f http://localhost:8080/api/health || exit 1
          echo "✅ Development deployment successful!"
        EOF
        
    - name: Cleanup old Docker images
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          # Keep only the last 3 images
          sudo docker images mobile-carwash --format "table {{.Tag}}" | grep -v TAG | grep -v latest | tail -n +4 | xargs -r sudo docker rmi mobile-carwash: || true
          
          # Clean up unused images
          sudo docker image prune -f
        EOF

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [test, build, deploy]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.deploy.result == 'success'
      run: |
        echo "✅ Development deployment successful!"
        echo "🚀 Version: dev-${{ github.sha }}"
        echo "🌐 Environment: Development"
        echo "📦 Commit: ${{ github.event.head_commit.message }}"
        
    - name: Notify failure
      if: needs.deploy.result == 'failure' || needs.build.result == 'failure' || needs.test.result == 'failure'
      run: |
        echo "❌ Development deployment failed!"
        echo "🔍 Check the logs for details"
        echo "📦 Commit: ${{ github.event.head_commit.message }}"
